# Testing the App Context Implementation

## Overview
This document outlines how to test the implementation of app-specific chat routing for URLs like `/chat/google-drive-utility`.

## What Was Implemented

1. **SpreadsheetsAgentService** (`src/services/spreadsheetsAgentService.js`)
   - New service to handle `/api/spreadsheets-agent/query` API calls
   - Supports streaming responses
   - Handles file uploads

2. **ChatContext Updates** (`src/contexts/ChatContext.js`)
   - Added `currentApp` state to track app-specific context
   - Added `setCurrentApp` function to set app context
   - Modified `sendStreamingMessage` and `sendStreamingMessageWithFile` to route to spreadsheets agent when `currentApp === 'google-drive-utility'`

3. **ChatLayout Updates** (`src/components/chat/ChatLayout/ChatLayout.js`)
   - Added logic to detect app IDs from URL parameters
   - Sets app context when URL matches pattern `/chat/app-id`
   - Clears app context for regular thread URLs

4. **Test Component** (`src/components/test/AppContextTest.js`)
   - Visual test component to verify app context functionality
   - Temporarily added to ChatArea for testing

## How to Test

### 1. Manual Testing via Browser

1. Start the development server: `npm start`
2. Navigate to `http://localhost:3000/chat`
3. You should see the "App Context Test Component" at the top
4. Test the following scenarios:

#### Test App Context Detection:
- Navigate to `http://localhost:3000/chat/google-drive-utility`
- The test component should show "Current App: google-drive-utility"
- Navigate to `http://localhost:3000/chat/slack-integration`
- The test component should show "Current App: slack-integration"
- Navigate to `http://localhost:3000/chat/some-thread-id`
- The test component should show "Current App: None"

#### Test Message Routing:
- Navigate to `http://localhost:3000/chat/google-drive-utility`
- Try sending a message in the chat
- Check browser console for network requests
- Should see requests to `/api/spreadsheets-agent/query` instead of `/api/chat/message/stream`

### 2. Console Testing

Open browser console and run:

```javascript
// Test 1: Check if app context is set correctly
console.log('Current app context:', window.location.pathname);

// Test 2: Check if the service is available
import('./src/services/spreadsheetsAgentService.js').then(module => {
  console.log('SpreadsheetsAgentService loaded:', module.default);
});

// Test 3: Test URL detection logic
const testUrls = ['/chat/google-drive-utility', '/chat/thread-123'];
testUrls.forEach(url => {
  const threadId = url.split('/chat/')[1];
  console.log(`URL: ${url} -> ThreadId: ${threadId}`);
});
```

### 3. Network Request Verification

1. Open browser DevTools -> Network tab
2. Navigate to `/chat/google-drive-utility`
3. Send a test message
4. Verify that the request goes to `/api/spreadsheets-agent/query` instead of `/api/chat/message/stream`

## Expected Behavior

### For App URLs (e.g., `/chat/google-drive-utility`):
- `currentApp` state should be set to the app ID
- Messages should route to `/api/spreadsheets-agent/query`
- File uploads should route to `/api/spreadsheets-agent/query` with FormData

### For Regular Thread URLs (e.g., `/chat/thread-123`):
- `currentApp` state should be `null`
- Messages should route to regular chat endpoints
- Normal chat functionality should work as before

### For Base Chat URL (`/chat`):
- `currentApp` state should be `null`
- Normal chat functionality should work as before

## Cleanup After Testing

After testing is complete, remove the test component:

1. Remove import from `src/components/chat/ChatArea/ChatArea.js`:
   ```javascript
   import AppContextTest from '../../test/AppContextTest';
   ```

2. Remove the test component from the render:
   ```jsx
   {/* Test Component - Remove this after testing */}
   <AppContextTest />
   ```

3. Delete test files:
   - `src/components/test/AppContextTest.js`
   - `src/test-app-context.js`
   - `test-implementation.md`

## Troubleshooting

### If app context is not being set:
- Check browser console for errors
- Verify that the URL parameter is being passed correctly to ChatLayout
- Check that the app ID exists in `src/data/apps.json`

### If messages are not routing to the correct API:
- Check browser console for network requests
- Verify that `currentApp` state is set correctly
- Check that the spreadsheetsAgentService is being imported correctly

### If there are compilation errors:
- Check that all imports are correct
- Verify that the service file exists and exports correctly
- Check for any syntax errors in the modified files
