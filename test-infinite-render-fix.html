<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Infinite Render Fix</title>
</head>
<body>
    <h1>Test Infinite Render Fix</h1>
    <p>Open the browser console and navigate to <a href="http://localhost:3000/chat" target="_blank">http://localhost:3000/chat</a></p>
    <p>Check if there are any "Maximum update depth exceeded" warnings in the console.</p>
    
    <h2>What was fixed:</h2>
    <ul>
        <li>Changed navigationCallback from useState to useRef in ChatContext</li>
        <li>This prevents re-renders when the callback is set</li>
        <li>The infinite loop was caused by setNavigationCallback triggering re-renders</li>
    </ul>
    
    <h2>Expected behavior:</h2>
    <ul>
        <li>No "Maximum update depth exceeded" warnings</li>
        <li>Chat page loads normally</li>
        <li>Navigation between threads works correctly</li>
    </ul>
</body>
</html>
