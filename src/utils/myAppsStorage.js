/**
 * Utility functions for managing My Apps in localStorage
 */

const MY_APPS_STORAGE_KEY = 'theinfini_my_apps';

/**
 * Get My Apps list from localStorage
 * @returns {string[]} Array of app IDs
 */
export const getMyApps = () => {
  try {
    const myApps = localStorage.getItem(MY_APPS_STORAGE_KEY);
    return myApps ? JSON.parse(myApps) : [];
  } catch (error) {
    console.error('Error reading My Apps from localStorage:', error);
    return [];
  }
};

/**
 * Add an app to My Apps list
 * @param {string} appId - The app ID to add
 * @returns {boolean} Success status
 */
export const addToMyApps = (appId) => {
  try {
    const myApps = getMyApps();
    
    // Check if app is already in the list
    if (myApps.includes(appId)) {
      return true; // Already exists, consider it a success
    }
    
    // Add the app ID to the list
    const updatedApps = [...myApps, appId];
    localStorage.setItem(MY_APPS_STORAGE_KEY, JSON.stringify(updatedApps));
    return true;
  } catch (error) {
    console.error('Error adding app to My Apps:', error);
    return false;
  }
};

/**
 * Remove an app from My Apps list
 * @param {string} appId - The app ID to remove
 * @returns {boolean} Success status
 */
export const removeFromMyApps = (appId) => {
  try {
    const myApps = getMyApps();
    const updatedApps = myApps.filter(id => id !== appId);
    localStorage.setItem(MY_APPS_STORAGE_KEY, JSON.stringify(updatedApps));
    return true;
  } catch (error) {
    console.error('Error removing app from My Apps:', error);
    return false;
  }
};

/**
 * Check if an app is in My Apps list
 * @param {string} appId - The app ID to check
 * @returns {boolean} Whether the app is in My Apps
 */
export const isInMyApps = (appId) => {
  const myApps = getMyApps();
  return myApps.includes(appId);
};

/**
 * Clear all My Apps
 * @returns {boolean} Success status
 */
export const clearMyApps = () => {
  try {
    localStorage.removeItem(MY_APPS_STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing My Apps:', error);
    return false;
  }
};

/**
 * Get My Apps with full app details from store
 * @param {Array} storeApps - Array of all available apps from store
 * @returns {Array} Array of My Apps with full details
 */
export const getMyAppsWithDetails = (storeApps) => {
  const myAppIds = getMyApps();
  return storeApps.filter(app => myAppIds.includes(app.id));
};
