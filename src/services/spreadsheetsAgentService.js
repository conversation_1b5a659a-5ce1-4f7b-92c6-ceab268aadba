import sessionManager from '../utils/sessionManager';

class SpreadsheetsAgentService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5529/api';
  }

  /**
   * Create streaming request for spreadsheets agent
   */
  async createStreamingRequest(endpoint, data, options = {}) {
    const token = localStorage.getItem('authToken');

    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add authentication header for authenticated users
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const error = new Error(errorData.message || `HTTP error! status: ${response.status}`);
      error.status = response.status;
      error.data = errorData;
      throw error;
    }

    return response;
  }

  /**
   * Parse Server-Sent Events stream or regular JSON response
   */
  async parseSSEStream(response, options = {}) {
    const {
      onStart = () => {},
      onToken = () => {},
      onComplete = () => {},
      onError = () => {}
    } = options;

    // Check if response is JSON (non-streaming)
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      try {
        const jsonData = await response.json();

        onStart();

        if (jsonData.success && jsonData.message) {
          // Handle non-streaming response - display the message directly
          const message = jsonData.message;

          // Simulate streaming by calling onToken with the full message
          onToken(message, message);
          onComplete();

          return {
            response: message,
            metadata: jsonData.data || {}
          };
        } else {
          // Handle error case
          const errorMessage = jsonData.message || 'Unknown error occurred';
          onError(new Error(errorMessage));
          throw new Error(errorMessage);
        }
      } catch (error) {
        onError(error);
        throw error;
      }
    }

    // Handle streaming response (original logic)
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';

    onStart();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            if (data === '[DONE]') {
              onComplete();
              break;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.token) {
                fullResponse += parsed.token;
                onToken(parsed.token, fullResponse);
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      onError(error);
      throw error;
    } finally {
      reader.releaseLock();
    }

    return {
      response: fullResponse,
      metadata: {}
    };
  }

  /**
   * Stream query to spreadsheets agent
   */
  async streamQuery(query, options = {}) {
    const { sessionId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const data = {
      query
    };

    // Include sessionId if provided for conversation continuity
    if (sessionId) {
      data.sessionId = sessionId;
    }

    const response = await this.createStreamingRequest('/spreadsheets-agent/query', data);
    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream query with file attachment to spreadsheets agent
   */
  async streamQueryWithFile(query, file, options = {}) {
    const { sessionId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    // For file uploads, we'll need to use FormData
    const formData = new FormData();
    formData.append('query', query);
    formData.append('file', file);

    if (sessionId) {
      formData.append('sessionId', sessionId);
    }

    const token = localStorage.getItem('authToken');
    const headers = {};

    // Add authentication header for authenticated users
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(`${this.baseURL}/spreadsheets-agent/query`, {
      method: 'POST',
      headers,
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const error = new Error(errorData.message || `HTTP error! status: ${response.status}`);
      error.status = response.status;
      error.data = errorData;
      throw error;
    }

    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Handle service errors
   */
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || error.response.data?.error || 'Server error occurred';
      const serviceError = new Error(message);
      serviceError.status = error.response.status;
      serviceError.data = error.response.data;
      return serviceError;
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error - please check your connection');
    } else {
      // Something else happened
      return error;
    }
  }
}

// Create and export singleton instance
const spreadsheetsAgentService = new SpreadsheetsAgentService();
export default spreadsheetsAgentService;
