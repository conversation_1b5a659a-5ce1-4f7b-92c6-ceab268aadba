import React, { useState } from 'react';
import googleAuthService from '../../services/googleAuthService';

const GoogleAuthTest = () => {
  const [status, setStatus] = useState(null);
  const [services, setServices] = useState(null);
  const [authResult, setAuthResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const testAuthStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await googleAuthService.getAuthStatus();
      setStatus(result);
      console.log('Auth Status:', result);
    } catch (err) {
      setError(err.message);
      console.error('Auth Status Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const testGetServices = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await googleAuthService.getAvailableServices();
      setServices(result);
      console.log('Available Services:', result);
    } catch (err) {
      setError(err.message);
      console.error('Services Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const testAuthFlow = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await googleAuthService.startAuthenticationFlow();
      setAuthResult(result);
      console.log('Auth Flow Result:', result);
    } catch (err) {
      setError(err.message);
      console.error('Auth Flow Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setStatus(null);
    setServices(null);
    setAuthResult(null);
    setError(null);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Google Auth Service Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <button onClick={testAuthStatus} disabled={loading} style={{ marginRight: '10px' }}>
          Test Auth Status
        </button>
        <button onClick={testGetServices} disabled={loading} style={{ marginRight: '10px' }}>
          Test Get Services
        </button>
        <button onClick={testAuthFlow} disabled={loading} style={{ marginRight: '10px' }}>
          Test Auth Flow
        </button>
        <button onClick={clearResults} style={{ marginRight: '10px' }}>
          Clear Results
        </button>
      </div>

      {loading && <p>Loading...</p>}
      
      {error && (
        <div style={{ 
          background: '#ffebee', 
          border: '1px solid #f44336', 
          padding: '10px', 
          borderRadius: '4px',
          marginBottom: '20px',
          color: '#d32f2f'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}

      {status && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Authentication Status:</h3>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto'
          }}>
            {JSON.stringify(status, null, 2)}
          </pre>
        </div>
      )}

      {services && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Available Services:</h3>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto'
          }}>
            {JSON.stringify(services, null, 2)}
          </pre>
        </div>
      )}

      {authResult && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Authentication Flow Result:</h3>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto'
          }}>
            {JSON.stringify(authResult, null, 2)}
          </pre>
        </div>
      )}

      <div style={{ marginTop: '30px', padding: '15px', background: '#e3f2fd', borderRadius: '4px' }}>
        <h4>Test Instructions:</h4>
        <ol>
          <li><strong>Test Auth Status:</strong> Check if user is authenticated with Google</li>
          <li><strong>Test Get Services:</strong> Retrieve available Google services and scopes</li>
          <li><strong>Test Auth Flow:</strong> Run the complete authentication flow</li>
        </ol>
        <p><strong>Note:</strong> These tests will make actual API calls to the backend. Make sure your backend server is running and the Google Auth endpoints are implemented.</p>
      </div>
    </div>
  );
};

export default GoogleAuthTest;
