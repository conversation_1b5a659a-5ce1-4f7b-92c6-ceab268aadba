import React, { useState } from 'react';
import googleAuthService from '../../../services/googleAuthService';
import './AppDetails.css';

const AppDetails = ({
  app,
  context,
  onBack,
  onAddToMyApps,
  onRemoveFromMyApps,
  onOpenApp,
  isInMyApps
}) => {
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authError, setAuthError] = useState(null);

  if (!app) return null;

  const handleGoogleDriveAuth = async () => {
    setIsLoading(true);
    setAuthError(null);

    try {
      const result = await googleAuthService.startAuthenticationFlow();

      if (result.authenticated) {
        // Check if token was refreshed
        if (result.tokenRefreshed && result.authUrl) {
          // Token was refreshed, open auth URL and then proceed
          window.open(result.authUrl, '_blank', 'noopener,noreferrer');
        }
        // User is authenticated (with or without token refresh), proceed to open the app
        onOpenApp(app);
      } else if (result.authUrl) {
        // User is not authenticated, redirect to Google OAuth
        window.open(result.authUrl, '_blank', 'noopener,noreferrer');
        // Still proceed to open the app so user can see the interface
        onOpenApp(app);
      }
    } catch (error) {
      console.error('Google authentication error:', error);
      setAuthError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrimaryAction = async () => {
    if (context === 'myApps') {
      // Check if this is the Google Drive utility app
      if (app.id === 'google-drive-utility') {
        await handleGoogleDriveAuth();
      } else {
        // Open other apps normally
        onOpenApp(app);
      }
    } else {
      // Add to My Apps
      onAddToMyApps(app);
    }
  };

  const handleSecondaryAction = () => {
    if (context === 'myApps') {
      // Remove from My Apps
      onRemoveFromMyApps(app);
    } else if (app.url) {
      // Open external URL
      window.open(app.url, '_blank', 'noopener,noreferrer');
    }
  };

  const toggleDescription = () => {
    setIsDescriptionExpanded(!isDescriptionExpanded);
  };

  const getAppIcon = (app) => {
    // Define custom SVG icons for each app as fallback
    const appIcons = {
      'google-drive-utility': (
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L2 7l10 5 10-5-10-5z" fill="#4285F4"/>
          <path d="M2 17l10 5 10-5M2 12l10 5 10-5" stroke="#34A853" strokeWidth="2" fill="none"/>
        </svg>
      ),
      'slack-integration': (
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
          <path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52z" fill="#E01E5A"/>
          <path d="M6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313z" fill="#E01E5A"/>
        </svg>
      ),
      'notion-workspace': (
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
          <path d="M4 4h16v16H4z" stroke="currentColor" strokeWidth="2" fill="none"/>
          <path d="M8 8h8M8 12h8M8 16h4" stroke="currentColor" strokeWidth="2"/>
        </svg>
      )
    };

    return appIcons[app.id] || (
      <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
        <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" fill="var(--card-bg)"/>
        <circle cx="12" cy="12" r="3" fill="currentColor"/>
      </svg>
    );
  };

  const renderAppIcon = () => {
    if (app.icon && (app.icon.endsWith('.png') || app.icon.endsWith('.jpg') || app.icon.endsWith('.svg'))) {
      return (
        <img
          src={app.icon}
          alt={`${app.name} logo`}
          className="app-details__icon-image"
          onError={(e) => {
            console.log(`Failed to load image: ${app.icon} for app: ${app.name}`);
            e.target.style.display = 'none';
            e.target.nextSibling.style.display = 'flex';
          }}
          onLoad={() => {
            console.log(`Successfully loaded image: ${app.icon} for app: ${app.name}`);
          }}
        />
      );
    }
    return null;
  };

  const renderFallbackIcon = () => (
    <div className="app-details__icon-fallback" style={{ display: app.icon ? 'none' : 'flex' }}>
      {getAppIcon(app)}
    </div>
  );

  const getDisplayDescription = () => {
    if (!app.description) return '';
    
    const cleanDescription = app.description.trim();
    if (cleanDescription.length <= 100) {
      return cleanDescription;
    }
    
    if (isDescriptionExpanded) {
      return cleanDescription;
    }
    
    return cleanDescription.substring(0, 100) + '...';
  };

  const shouldShowToggle = () => {
    return app.description && app.description.trim().length > 100;
  };

  return (
    <div className="app-details">
      {/* Header with back button */}
      <div className="app-details__header">
        <button
          className="app-details__back-btn"
          onClick={onBack}
          type="button"
          aria-label="Back to apps"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="15,18 9,12 15,6"/>
          </svg>
          <span>Back</span>
        </button>
      </div>

      {/* Main content */}
      <div className="app-details__content">
        {/* App icon */}
        <div className="app-details__icon">
          {renderAppIcon()}
          {renderFallbackIcon()}
        </div>

        {/* App information */}
        <div className="app-details__info">
          <h2 className="app-details__name">{app.name}</h2>
          
          {app.version && (
            <div className="app-details__version">
              <span className="app-details__version-label">Version:</span>
              <span className="app-details__version-value">{app.version}</span>
            </div>
          )}

          {app.category && (
            <div className="app-details__category">
              <span className="app-details__category-label">Category:</span>
              <span className="app-details__category-value">{app.category}</span>
            </div>
          )}

          {app.description && (
            <div className="app-details__description">
              <h3 className="app-details__description-title">Description</h3>
              <p className="app-details__description-text">
                {getDisplayDescription()}
              </p>
              {shouldShowToggle() && (
                <button
                  className="app-details__description-toggle"
                  onClick={toggleDescription}
                  type="button"
                >
                  {isDescriptionExpanded ? 'Show less' : 'Show more'}
                </button>
              )}
            </div>
          )}

          {/* Error message for Google Drive authentication */}
          {authError && app.id === 'google-drive-utility' && (
            <div className="app-details__error">
              <p className="app-details__error-text">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"/>
                  <line x1="15" y1="9" x2="9" y2="15"/>
                  <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                {authError}
              </p>
            </div>
          )}

          {/* Action buttons */}
          <div className="app-details__actions">
            {context === 'myApps' ? (
              <>
                {/* Open button for My Apps */}
                <button
                  className="app-details__open-btn"
                  onClick={handlePrimaryAction}
                  type="button"
                  disabled={isLoading}
                >
                  {isLoading && app.id === 'google-drive-utility' ? (
                    <>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="app-details__loading-spinner">
                        <path d="M21 12a9 9 0 11-6.219-8.56"/>
                      </svg>
                      Authenticating...
                    </>
                  ) : (
                    <>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                        <polyline points="15,3 21,3 21,9"/>
                        <line x1="10" y1="14" x2="21" y2="3"/>
                      </svg>
                      Open
                    </>
                  )}
                </button>

                {/* Remove from My Apps button */}
                <button
                  className="app-details__remove-btn"
                  onClick={handleSecondaryAction}
                  type="button"
                  title="Remove from My Apps"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M3 6h18"/>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                  </svg>
                  Remove
                </button>
              </>
            ) : (
              <>
                {/* Primary button for Store - Add or Remove */}
                {isInMyApps ? (
                  <button
                    className="app-details__remove-btn"
                    onClick={() => onRemoveFromMyApps(app)}
                    type="button"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M3 6h18"/>
                      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                    </svg>
                    Remove from My Apps
                  </button>
                ) : (
                  <button
                    className="app-details__add-btn"
                    onClick={handlePrimaryAction}
                    type="button"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M12 5v14"/>
                      <path d="M5 12h14"/>
                    </svg>
                    Add to My Apps
                  </button>
                )}

                {/* External link button for Store apps */}
                {app.url && (
                  <button
                    className="app-details__external-btn"
                    onClick={handleSecondaryAction}
                    type="button"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                      <polyline points="15,3 21,3 21,9"/>
                      <line x1="10" y1="14" x2="21" y2="3"/>
                    </svg>
                    Visit Website
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppDetails;
