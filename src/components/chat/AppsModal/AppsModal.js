import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Modal from '../../common/Modal';
import AppDetails from './AppDetails';
import appsData from '../../../data/apps.json';
import { getMyAppsWithDetails, addToMyApps, removeFromMyApps, isInMyApps } from '../../../utils/myAppsStorage';
import googleAuthService from '../../../services/googleAuthService';
import './AppsModal.css';



const AppsModal = ({ isOpen, onClose }) => {
  const [storeApps, setStoreApps] = useState([]);
  const [myApps, setMyApps] = useState([]);
  const [selectedApp, setSelectedApp] = useState(null);
  const [showAppDetails, setShowAppDetails] = useState(false);
  const [activeTab, setActiveTab] = useState('myApps');
  const [currentContext, setCurrentContext] = useState('myApps'); // 'myApps' or 'store'
  const navigate = useNavigate();

  useEffect(() => {
    // Load apps from JSON data
    const store = appsData.store || [];
    setStoreApps(store);

    // Load My Apps with details
    const myAppsWithDetails = getMyAppsWithDetails(store);
    setMyApps(myAppsWithDetails);
  }, []);

  // Refresh My Apps when modal opens
  useEffect(() => {
    if (isOpen) {
      const myAppsWithDetails = getMyAppsWithDetails(storeApps);
      setMyApps(myAppsWithDetails);
    }
  }, [isOpen, storeApps]);

  const handleAppClick = (app, context) => {
    if (context === 'myApps') {
      // Directly open the app from My Apps
      handleOpenApp(app);
    } else {
      // Show details page for store apps
      setSelectedApp(app);
      setCurrentContext(context);
      setShowAppDetails(true);
    }
  };

  const handleBackToApps = () => {
    setShowAppDetails(false);
    setSelectedApp(null);
    setCurrentContext(activeTab);
  };

  const handleModalClose = () => {
    // Reset to apps grid when modal is closed
    setShowAppDetails(false);
    setSelectedApp(null);
    setActiveTab('myApps');
    setCurrentContext('myApps');
    onClose();
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setCurrentContext(tab);
  };

  const handleAddToMyApps = (app) => {
    const success = addToMyApps(app.id);
    if (success) {
      // Refresh My Apps list
      const myAppsWithDetails = getMyAppsWithDetails(storeApps);
      setMyApps(myAppsWithDetails);

      // Go back to apps grid and switch to My Apps tab
      setShowAppDetails(false);
      setSelectedApp(null);
      setActiveTab('myApps');
      setCurrentContext('myApps');
    }
  };

  const handleRemoveFromMyApps = (app) => {
    const success = removeFromMyApps(app.id);
    if (success) {
      // Refresh My Apps list
      const myAppsWithDetails = getMyAppsWithDetails(storeApps);
      setMyApps(myAppsWithDetails);

      // If we're viewing this app's details from store, go back to store view
      if (currentContext === 'store' && showAppDetails) {
        setShowAppDetails(false);
        setSelectedApp(null);
        setActiveTab('store');
        setCurrentContext('store');
      }
    }
  };

  const handleOpenApp = async (app) => {
    // Check if this is the Google Drive utility app
    if (app.id === 'google-drive-utility') {
      try {
        const result = await googleAuthService.startAuthenticationFlow();

        if (result.authenticated) {
          // Check if token was refreshed
          if (result.tokenRefreshed && result.authUrl) {
            // Token was refreshed, open auth URL and then navigate
            window.open(result.authUrl, '_blank', 'noopener,noreferrer');
            // Still navigate to the app page
            navigate(`/chat/${app.id}`);
            handleModalClose();
          } else {
            // User is already authenticated with valid token, proceed to navigate
            navigate(`/chat/${app.id}`);
            handleModalClose();
          }
        } else if (result.authUrl) {
          // User is not authenticated, redirect to Google OAuth
          window.open(result.authUrl, '_blank', 'noopener,noreferrer');
          // Navigate to the app page anyway so user can see the interface
          navigate(`/chat/${app.id}`);
          handleModalClose();
        }
      } catch (error) {
        console.error('Google authentication error:', error);
        // You might want to show an error message to the user here
        // For now, we'll still navigate to the app page
        navigate(`/chat/${app.id}`);
        handleModalClose();
      }
    } else {
      // Navigate to other apps normally
      navigate(`/chat/${app.id}`);
      handleModalClose();
    }
  };

  const getAppIcon = (app) => {
    // Define custom SVG icons for each app as fallback
    const appIcons = {
      'google-drive-utility': (
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L2 7l10 5 10-5-10-5z" fill="#4285F4"/>
          <path d="M2 17l10 5 10-5M2 12l10 5 10-5" stroke="#34A853" strokeWidth="2" fill="none"/>
        </svg>
      ),
      'slack-integration': (
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
          <path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52z" fill="#E01E5A"/>
          <path d="M6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313z" fill="#E01E5A"/>
        </svg>
      ),
      'notion-workspace': (
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
          <path d="M4 4h16v16H4z" stroke="currentColor" strokeWidth="2" fill="none"/>
          <path d="M8 8h8M8 12h8M8 16h4" stroke="currentColor" strokeWidth="2"/>
        </svg>
      )
    };

    return appIcons[app.id] || (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
        <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" fill="var(--card-bg)"/>
        <circle cx="12" cy="12" r="3" fill="currentColor"/>
      </svg>
    );
  };

  const renderAppIcon = (app) => {
    if (app.icon && (app.icon.endsWith('.png') || app.icon.endsWith('.jpg') || app.icon.endsWith('.svg'))) {
      return (
        <>
          <img
            src={app.icon}
            alt={`${app.name} logo`}
            width="48"
            height="48"
            style={{ objectFit: 'contain' }}
            onError={(e) => {
              console.log(`Failed to load image: ${app.icon} for app: ${app.name}`);
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'block';
            }}
            onLoad={() => {
              console.log(`Successfully loaded image: ${app.icon} for app: ${app.name}`);
            }}
          />
          <div style={{ display: 'none' }}>
            {getAppIcon(app)}
          </div>
        </>
      );
    }

    // Use custom icon if no image path
    return getAppIcon(app);
  };

  const getCurrentApps = () => {
    return activeTab === 'myApps' ? myApps : storeApps;
  };

  const renderTabContent = () => {
    const currentApps = getCurrentApps();

    if (currentApps.length > 0) {
      return (
        <div className="apps-modal__grid">
          {currentApps.map((app) => (
            <div
              key={app.id}
              className="apps-modal__app-item"
              onClick={() => handleAppClick(app, activeTab)}
              title={app.description}
            >
              <div className="apps-modal__app-icon">
                {renderAppIcon(app)}
              </div>
              <span className="apps-modal__app-name">{app.name}</span>
            </div>
          ))}
        </div>
      );
    } else {
      const placeholderContent = activeTab === 'myApps'
        ? {
            title: "No Apps Added Yet",
            description: "Browse the Apps Store to add apps to your collection."
          }
        : {
            title: "Apps Coming Soon",
            description: "We're working on bringing you powerful apps and integrations to enhance your chat experience."
          };

      return (
        <div className="apps-modal__placeholder">
          <div className="apps-modal__placeholder-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
              <rect x="3" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              <rect x="14" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              <rect x="3" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              <rect x="14" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
          <h3 className="apps-modal__placeholder-title">{placeholderContent.title}</h3>
          <p className="apps-modal__placeholder-description">
            {placeholderContent.description}
          </p>
        </div>
      );
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleModalClose}
      title={showAppDetails ? selectedApp?.name || "App Details" : "Apps"}
      size="custom"
      className="apps-modal"
      closeOnOverlayClick={true}
    >
      <div className="apps-modal__content">
        {showAppDetails ? (
          <AppDetails
            app={selectedApp}
            context={currentContext}
            onBack={handleBackToApps}
            onAddToMyApps={handleAddToMyApps}
            onRemoveFromMyApps={handleRemoveFromMyApps}
            onOpenApp={handleOpenApp}
            isInMyApps={isInMyApps(selectedApp?.id)}
          />
        ) : (
          <>
            {/* Tabs */}
            <div className="apps-modal__tabs">
              <button
                className={`apps-modal__tab ${activeTab === 'myApps' ? 'apps-modal__tab--active' : ''}`}
                onClick={() => handleTabChange('myApps')}
              >
                My Apps
              </button>
              <button
                className={`apps-modal__tab ${activeTab === 'store' ? 'apps-modal__tab--active' : ''}`}
                onClick={() => handleTabChange('store')}
              >
                Apps Store
              </button>
            </div>

            {/* Tab Content */}
            <div className="apps-modal__tab-content">
              {renderTabContent()}
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default AppsModal;
